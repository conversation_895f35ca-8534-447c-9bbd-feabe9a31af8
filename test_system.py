#!/usr/bin/env python3
"""
Test script for the NLP Question-Answer System
"""

import sys
from pathlib import Path
from nlp_qa_system import NLPQASystem, NLPFileParser, ChromaDBManager, OllamaClient

def test_file_parsing():
    """Test the NLP file parsing functionality."""
    print("Testing file parsing...")
    
    if not Path("NLP_Coursera.py").exists():
        print("❌ NLP_Coursera.py not found")
        return False
    
    parser = NLPFileParser(["NLP_Coursera.py"], ["PE_SU24_1"])
    qa_pairs = parser.parse_file()
    
    if not qa_pairs:
        print("❌ No QA pairs parsed")
        return False
    
    print(f"✅ Parsed {len(qa_pairs)} QA pairs")
    
    # Show first few examples
    for i, qa_pair in enumerate(qa_pairs[:3]):
        print(f"  {i+1}. Q{qa_pair.question_number}: {qa_pair.question_title}")
    
    return True

def test_chromadb():
    """Test ChromaDB functionality."""
    print("\nTesting ChromaDB...")
    
    try:
        db_manager = ChromaDBManager("test_collection")
        db_manager.initialize_collection()
        print("✅ ChromaDB initialization successful")
        
        # Test with sample data
        from nlp_qa_system import QAPair
        sample_qa = QAPair(
            question_number=999,
            question_title="Test Question",
            question_description="This is a test",
            answer_code="print('test')",
            full_content="Question 999: Test Question\nDescription: This is a test\nSolution:\nprint('test')"
        )
        
        db_manager.index_qa_pairs([sample_qa])
        print("✅ ChromaDB indexing successful")
        
        # Test search
        results = db_manager.search_qa_pairs("test question", n_results=1)
        if results:
            print("✅ ChromaDB search successful")
        else:
            print("⚠️ ChromaDB search returned no results")
        
        return True
        
    except Exception as e:
        print(f"❌ ChromaDB test failed: {e}")
        return False

def test_ollama():
    """Test Ollama connectivity."""
    print("\nTesting Ollama...")
    
    client = OllamaClient()
    
    if not client.is_available():
        print("⚠️ Ollama service not available")
        print("   This is optional - the system will work without it")
        return True
    
    print("✅ Ollama service is available")
    
    # Test simple generation
    try:
        response = client.generate_response("Hello, this is a test.")
        if response and not response.startswith("Error"):
            print("✅ Ollama response generation successful")
            print(f"   Sample response: {response[:100]}...")
        else:
            print(f"⚠️ Ollama response generation issue: {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ollama test failed: {e}")
        return False

def test_full_system():
    """Test the complete system integration."""
    print("\nTesting full system integration...")
    
    try:
        system = NLPQASystem()
        
        if not system.initialize_system():
            print("❌ System initialization failed")
            return False
        
        print("✅ System initialization successful")
        
        # Test a sample query
        test_queries = [
            "How to tokenize text?",
            "What is stemming?",
            "How to remove stopwords?"
        ]
        
        for query in test_queries:
            print(f"\nTesting query: '{query}'")
            result = system.ask_question(query, n_results=2)
            
            if result['status'] == 'success' and result['search_results']:
                print(f"✅ Query successful - found {len(result['search_results'])} results")
                
                # Show first result
                first_result = result['search_results'][0]
                metadata = first_result['metadata']
                relevance = 1 - first_result['distance']
                print(f"   Best match: Q{metadata['question_number']} (relevance: {relevance:.1%})")
                
            else:
                print(f"⚠️ Query returned no results")
            
            # Check if we got an LLM response
            if result['llm_response'] and not result['llm_response'].startswith("Error"):
                print("✅ LLM response generated")
            else:
                print("⚠️ No LLM response (Ollama may not be available)")
        
        return True
        
    except Exception as e:
        print(f"❌ Full system test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("="*60)
    print("NLP Question-Answer System - Test Suite")
    print("="*60)
    
    tests = [
        ("File Parsing", test_file_parsing),
        ("ChromaDB", test_chromadb),
        ("Ollama", test_ollama),
        ("Full System", test_full_system)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready to use.")
    elif passed >= total - 1:
        print("⚠️ Most tests passed. The system should work with minor limitations.")
    else:
        print("❌ Multiple tests failed. Please check the setup.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
