""""Question 1: (2 marks)
Write code to convert nationality adjectives like Canadian and Australian to their
corresponding nouns Canada and Australia
Example:
Input:
['Argentinian', 'Australian', ' Canadian']
Output:
('Argentina', 'Australia', 'Canada']"""

import nltk
from nltk.corpus import wordnet as wn

# Ensure the necessary resources are downloaded
nltk.download('wordnet')

# Define a mapping of nationality adjectives to country names
nationality_to_country = {
    'Argentinian': 'Argentina',
    'Australian': 'Australia',
    'Canadian': 'Canada',
    'American': 'United States',
    'British': 'United Kingdom',
    'French': 'France',
    'German': 'Germany',
    # Add more mappings as needed
}

def convert_nationality_to_country(nationalities):
    return [nationality_to_country.get(nat, 'Unknown') for nat in nationalities]

# Example input
nationalities = ['Argentinian', 'Australian', 'Canadian']
countries = convert_nationality_to_country(nationalities)

print(countries)
