#!/usr/bin/env python3
"""
Demo script for the NLP Question-Answer System
Shows complete Q&A pairs with formatted output
"""

from nlp_qa_system import NLPQASystem

def format_qa_pair(qa_pair):
    """Format a QA pair for display."""
    print(f"\n{'='*80}")
    print(f"📝 Question {qa_pair.question_number}: {qa_pair.question_title}")
    print(f"{'='*80}")
    
    if qa_pair.question_description:
        print(f"📖 Description: {qa_pair.question_description}")
        print()
    
    print("💻 Solution:")
    print("-" * 40)
    
    # Clean up and display the code
    code_lines = qa_pair.answer_code.split('\n')
    for line in code_lines:
        if line.strip():  # Skip empty lines
            # Remove leading # for commented code
            if line.strip().startswith('#'):
                line = line.strip()[1:].strip()
            print(f"  {line}")

def demo_search_functionality():
    """Demonstrate the search functionality."""
    print("🚀 Initializing NLP QA System...")
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    print(f"✅ System ready! Loaded {len(system.qa_pairs)} question-answer pairs.")
    
    # Demo queries
    demo_queries = [
        "How to tokenize text?",
        "How to remove stopwords?",
        "How to perform stemming?",
        "How to find similarity between words?",
        "How to extract named entities?"
    ]
    
    for query in demo_queries:
        print(f"\n{'🔍 SEARCHING FOR: ' + query:^80}")
        print("="*80)
        
        result = system.ask_question(query, n_results=2)
        
        if result['search_results']:
            for i, search_result in enumerate(result['search_results'], 1):
                metadata = search_result['metadata']
                relevance = 1 - search_result['distance']
                
                print(f"\n[{i}] 🎯 Match: Q{metadata['question_number']} - {metadata['question_title']}")
                print(f"    📊 Relevance: {relevance:.1%}")
                
                if metadata.get('question_description'):
                    print(f"    📝 Description: {metadata['question_description']}")
                
                # Show a snippet of the solution
                content_lines = search_result['content'].split('\n')
                solution_started = False
                code_snippet = []
                
                for line in content_lines:
                    if line.startswith('Solution:'):
                        solution_started = True
                        continue
                    if solution_started and line.strip():
                        code_snippet.append(line)
                        if len(code_snippet) >= 3:  # Show first 3 lines
                            break
                
                if code_snippet:
                    print("    💻 Code snippet:")
                    for line in code_snippet:
                        print(f"       {line}")
                    print("       ...")
        
        print("\n" + "-"*80)

def show_available_questions():
    """Show all available questions."""
    print("🚀 Loading NLP QA System...")
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    print(f"\n📚 Available Questions ({len(system.qa_pairs)} total):")
    print("="*80)
    
    for qa_pair in system.qa_pairs:
        print(f"{qa_pair.question_number:2d}. {qa_pair.question_title}")
        if qa_pair.question_description:
            # Show first 60 chars of description
            desc = qa_pair.question_description
            if len(desc) > 60:
                desc = desc[:60] + "..."
            print(f"    📝 {desc}")
        print()

def show_specific_question(question_number):
    """Show a specific question in detail."""
    print("🚀 Loading NLP QA System...")
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    qa_pair = system.get_qa_pair_by_number(question_number)
    if qa_pair:
        format_qa_pair(qa_pair)
    else:
        print(f"❌ Question {question_number} not found")

def main():
    """Main demo function."""
    print("="*80)
    print("🎓 NLP Question-Answer System Demo")
    print("="*80)
    
    while True:
        print("\nChoose an option:")
        print("1. 🔍 Demo search functionality")
        print("2. 📚 Show all available questions")
        print("3. 📝 Show specific question details")
        print("4. 🚪 Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '1':
            demo_search_functionality()
        elif choice == '2':
            show_available_questions()
        elif choice == '3':
            try:
                question_num = int(input("Enter question number: "))
                show_specific_question(question_num)
            except ValueError:
                print("❌ Please enter a valid number")
        elif choice == '4':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-4.")

if __name__ == "__main__":
    main()
