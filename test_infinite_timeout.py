#!/usr/bin/env python3
"""
Test script to verify infinite timeout works
"""

from nlp_qa_system import NLPQASystem

def test_infinite_timeout():
    """Test the system with infinite timeout."""
    print("🚀 Testing infinite timeout for AI responses...")
    
    # Initialize system
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return False
    
    print(f"✅ System ready with {len(system.qa_pairs)} Q&A pairs")
    
    # Test with a simple question
    print("\n🔍 Testing with question: 'What is tokenization?'")
    print("⏳ This will wait indefinitely for the AI response...")
    print("💡 If Qwen3:4b is not installed, it may take a very long time to download and respond")
    
    try:
        result = system.ask_question("What is tokenization?", n_results=2)
        
        print("\n📋 Search Results:")
        for i, search_result in enumerate(result['search_results'], 1):
            metadata = search_result['metadata']
            relevance = 1 - search_result['distance']
            question_type = metadata.get('question_type', 'coursework')
            print(f"{i}. Q{metadata['question_number']}: {metadata['question_title']} ({question_type}) - {relevance:.1%}")
        
        print(f"\n🤖 AI Response:")
        if result['llm_response'].startswith("Error"):
            print(f"❌ {result['llm_response']}")
        else:
            print(f"✅ Response received ({len(result['llm_response'])} characters)")
            print(f"Preview: {result['llm_response'][:200]}...")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user (Ctrl+C)")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("="*60)
    print("Infinite Timeout Test for NLP QA System")
    print("="*60)
    print("This test will wait indefinitely for AI responses.")
    print("Press Ctrl+C to cancel if needed.")
    print("="*60)
    
    success = test_infinite_timeout()
    
    if success:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed or was interrupted.")
