#!/usr/bin/env python3
"""
Test script to demonstrate debug features and streaming
"""

from nlp_qa_system import NLPQASystem

def test_debug_features():
    """Test the debug and streaming features."""
    print("🚀 Testing Debug Features...")
    
    # Initialize system
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return False
    
    print(f"✅ System ready with {len(system.qa_pairs)} Q&A pairs")
    
    # Test question
    test_question = "How to tokenize text?"
    
    print(f"\n{'='*80}")
    print("🧪 TESTING DEBUG MODE (Non-streaming)")
    print(f"{'='*80}")
    print(f"Question: {test_question}")
    print(f"{'='*80}")
    
    # Test with debug mode but no streaming first
    result = system.ask_question(test_question, n_results=2, debug=True, stream=False)
    
    print(f"\n{'='*80}")
    print("📊 RESULT SUMMARY:")
    print(f"{'='*80}")
    print(f"Status: {result['status']}")
    print(f"Search results found: {len(result['search_results'])}")
    print(f"Response length: {len(result['llm_response'])} characters")
    
    if result['llm_response'].startswith("Error"):
        print(f"❌ AI Response: {result['llm_response']}")
    else:
        print(f"✅ AI Response received successfully")
        print(f"Preview: {result['llm_response'][:100]}...")
    
    print(f"\n{'='*80}")
    print("🧪 TESTING STREAMING MODE")
    print(f"{'='*80}")
    print("Note: If Ollama is available, you'll see the response stream in real-time")
    print(f"{'='*80}")
    
    # Test with streaming
    result_stream = system.ask_question(test_question, n_results=2, debug=True, stream=True)
    
    print(f"\n{'='*80}")
    print("✅ DEBUG FEATURES TEST COMPLETE")
    print(f"{'='*80}")
    
    return True

def show_prompt_example():
    """Show what a typical prompt looks like."""
    print(f"\n{'='*80}")
    print("📝 EXAMPLE OF SYSTEM PROMPT STRUCTURE")
    print(f"{'='*80}")
    
    example_prompt = """You are an expert NLP assistant. Answer the user's question based on the provided NLP question-answer pairs from a coursework collection.

User Question: How to tokenize text?

Relevant NLP Question-Answer pairs:

--- Result 1 ---
Question 3: How to tokenize a given text?
Description: Print the tokens of the given text document
Solution:
import nltk
tokens = nltk.word_tokenize(text)
for token in tokens:
    print(token)
(Relevance score: 0.453)

--- Result 2 ---
Question 6: How to tokenize text with stopwords as delimiters?
Description: Tokenize the given text with stop words ("is","the","was") as delimiters
Solution:
stop_words_and_delims = ['was', 'is', 'the', '.', ',', '-', '!', '?']
for r in stop_words_and_delims:
    text = text.replace(r, 'DELIM')
words = [t.strip() for t in text.split('DELIM')]
(Relevance score: 0.241)

Please provide a comprehensive answer that:
1. Directly addresses the user's question
2. No explaination needed, just show the code

Answer:"""
    
    print(example_prompt)
    print(f"{'='*80}")
    print("📊 PROMPT ANALYSIS:")
    print(f"Total length: {len(example_prompt)} characters")
    print(f"Contains: System instructions + User question + Context + Guidelines")
    print(f"{'='*80}")

if __name__ == "__main__":
    print("="*80)
    print("🔍 Debug Features Test for NLP QA System")
    print("="*80)
    
    # Show example prompt structure
    show_prompt_example()
    
    # Test debug features
    test_debug_features()
    
    print(f"\n{'='*80}")
    print("🎯 HOW TO USE DEBUG MODE:")
    print(f"{'='*80}")
    print("1. Interactive debug mode:")
    print("   python debug_nlp_qa.py")
    print()
    print("2. Single query with debug:")
    print("   python debug_nlp_qa.py -q \"How to remove stopwords?\"")
    print()
    print("3. Debug without streaming:")
    print("   python debug_nlp_qa.py --no-stream")
    print()
    print("4. Test this script:")
    print("   python test_debug_features.py")
    print(f"{'='*80}")
