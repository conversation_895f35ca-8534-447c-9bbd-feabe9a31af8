#!/usr/bin/env python3
"""
Quick command-line tool to list questions in the database
"""

import argparse
from nlp_qa_system import NLPQASystem

def list_questions(question_type=None, show_code=False, limit=None):
    """List questions with optional filtering."""
    print("🔍 Loading questions from database...")
    
    # Initialize system
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    # Filter questions by type if specified
    questions = system.qa_pairs
    if question_type:
        questions = [q for q in questions if q.question_type == question_type]
    
    # Sort by question number
    questions.sort(key=lambda x: x.question_number)
    
    # Apply limit if specified
    if limit:
        questions = questions[:limit]
    
    print(f"\n✅ Found {len(questions)} questions")
    if question_type:
        print(f"   Filtered by type: {question_type}")
    
    print(f"\n{'='*100}")
    print(f"{'#':<6} {'Type':<15} {'Title':<60} {'Source':<20}")
    print(f"{'='*100}")
    
    for qa_pair in questions:
        # Truncate title if too long
        title = qa_pair.question_title
        if len(title) > 57:
            title = title[:54] + "..."
        
        # Truncate source if too long
        source = qa_pair.source_file.split('/')[-1] if qa_pair.source_file else "N/A"
        if len(source) > 17:
            source = source[:14] + "..."
        
        print(f"{qa_pair.question_number:<6} {qa_pair.question_type:<15} {title:<60} {source:<20}")
        
        if show_code:
            # Show first 2 lines of code
            code_lines = qa_pair.answer_code.strip().split('\n')
            if code_lines and code_lines[0]:
                print(f"       💻 {code_lines[0]}")
                if len(code_lines) > 1 and code_lines[1]:
                    print(f"          {code_lines[1]}")
                if len(code_lines) > 2:
                    print(f"          ... ({len(code_lines) - 2} more lines)")
            print()

def show_question_types():
    """Show available question types and counts."""
    print("🔍 Loading question types...")
    
    # Initialize system
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    # Count by type
    type_counts = {}
    for qa_pair in system.qa_pairs:
        question_type = qa_pair.question_type
        type_counts[question_type] = type_counts.get(question_type, 0) + 1
    
    print(f"\n📊 Question Types in Database:")
    print("-" * 40)
    for question_type, count in sorted(type_counts.items()):
        print(f"{question_type:<20} {count:>3} questions")
    
    print(f"\nTotal: {len(system.qa_pairs)} questions")

def main():
    """Main function with command-line arguments."""
    parser = argparse.ArgumentParser(
        description="List questions stored in the NLP QA database",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python list_questions.py                           # List all questions
  python list_questions.py --type coursework         # List only coursework questions
  python list_questions.py --type previous_exam      # List only previous exam questions
  python list_questions.py --code                    # Show code previews
  python list_questions.py --limit 10                # Show first 10 questions
  python list_questions.py --types                   # Show question types and counts
        """
    )
    
    parser.add_argument(
        '--type',
        choices=['coursework', 'previous_exam', 'exam', 'tonghop'],
        help='Filter by question type'
    )
    
    parser.add_argument(
        '--code',
        action='store_true',
        help='Show code previews for each question'
    )
    
    parser.add_argument(
        '--limit',
        type=int,
        help='Limit number of questions to show'
    )
    
    parser.add_argument(
        '--types',
        action='store_true',
        help='Show question types and counts'
    )
    
    args = parser.parse_args()
    
    if args.types:
        show_question_types()
    else:
        list_questions(
            question_type=args.type,
            show_code=args.code,
            limit=args.limit
        )

if __name__ == "__main__":
    main()
