#!/usr/bin/env python3
"""
Debug version of NLP QA System - Shows all prompts and streams AI responses
"""

import argparse
import sys
from pathlib import Path
from nlp_qa_system import NLPQASystem

def run_debug_mode(system, stream=True):
    """Run the system in debug mode with full prompt visibility."""
    print("\n" + "="*80)
    print("🔍 DEBUG MODE - NLP Question-Answer System")
    print("="*80)
    print("This mode shows:")
    print("• Complete system prompts sent to AI")
    print("• Search result details")
    print("• Real-time streaming responses (if enabled)")
    print("• All internal processing steps")
    print("="*80)
    print("Commands:")
    print("  'quit' or 'exit' - Exit the system")
    print("  'stream on/off' - Toggle streaming mode")
    print("  'help' - Show this help message")
    print("="*80)
    
    streaming_enabled = stream
    
    while True:
        try:
            user_input = input(f"\n🤖 Your question (streaming: {'ON' if streaming_enabled else 'OFF'}): ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if user_input.lower() in ['help', 'h']:
                print("\nCommands:")
                print("  'quit' or 'exit' - Exit the system")
                print("  'stream on/off' - Toggle streaming mode")
                print("  'help' - Show this help message")
                continue
            
            if user_input.lower().startswith('stream'):
                if 'on' in user_input.lower():
                    streaming_enabled = True
                    print("✅ Streaming mode enabled")
                elif 'off' in user_input.lower():
                    streaming_enabled = False
                    print("✅ Streaming mode disabled")
                else:
                    print(f"ℹ️ Streaming is currently {'ON' if streaming_enabled else 'OFF'}")
                continue
            
            if not user_input:
                continue
            
            print(f"\n{'🔍 PROCESSING QUESTION':^80}")
            print("="*80)
            print(f"Question: {user_input}")
            print(f"Streaming: {'Enabled' if streaming_enabled else 'Disabled'}")
            print("="*80)
            
            # Process the question with debug mode
            result = system.ask_question(user_input, n_results=3, debug=True, stream=streaming_enabled)
            
            # If streaming was disabled, show the response
            if not streaming_enabled:
                print(f"\n{'🤖 AI RESPONSE':^80}")
                print("="*80)
                if result['llm_response'].startswith("Error"):
                    print(f"❌ {result['llm_response']}")
                else:
                    print(result['llm_response'])
                print("="*80)
            
            print(f"\n{'✅ PROCESSING COMPLETE':^80}")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def run_single_debug_query(system, query, stream=True):
    """Run a single query in debug mode."""
    print(f"\n{'🔍 DEBUG SINGLE QUERY':^80}")
    print("="*80)
    print(f"Question: {query}")
    print(f"Streaming: {'Enabled' if stream else 'Disabled'}")
    print("="*80)
    
    # Process the question with debug mode
    result = system.ask_question(query, n_results=3, debug=True, stream=stream)
    
    # If streaming was disabled, show the response
    if not stream:
        print(f"\n{'🤖 AI RESPONSE':^80}")
        print("="*80)
        if result['llm_response'].startswith("Error"):
            print(f"❌ {result['llm_response']}")
        else:
            print(result['llm_response'])
        print("="*80)
    
    print(f"\n{'✅ PROCESSING COMPLETE':^80}")

def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(
        description="Debug NLP Question-Answer System - Shows all prompts and streams responses",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python debug_nlp_qa.py                           # Interactive debug mode
  python debug_nlp_qa.py -q "How to tokenize text?" # Single debug query
  python debug_nlp_qa.py --no-stream               # Disable streaming
        """
    )
    
    parser.add_argument(
        '-q', '--query',
        type=str,
        help='Ask a single question in debug mode and exit'
    )
    
    parser.add_argument(
        '--model',
        type=str,
        default='qwen3:4b',
        help='Ollama model to use (default: qwen3:4b)'
    )
    
    parser.add_argument(
        '--no-stream',
        action='store_true',
        help='Disable streaming mode (show complete response at once)'
    )
    
    parser.add_argument(
        '--results',
        type=int,
        default=3,
        help='Number of search results to return (default: 3)'
    )
    
    args = parser.parse_args()
    
    # Initialize system with coursework and exam questions
    print("🚀 Initializing Debug NLP QA System...")
    system = NLPQASystem(ollama_model=args.model)
    
    if not system.initialize_system():
        print("❌ Failed to initialize system. Please check the setup.")
        sys.exit(1)
    
    print(f"✅ System ready! Loaded {len(system.qa_pairs)} question-answer pairs.")
    
    # Check Ollama availability
    if not system.ollama_client.is_available():
        print("⚠️  Warning: Ollama service not available.")
        print("   You'll still get search results and see prompts, but no AI responses.")
        print("   To enable AI responses, please install and start Ollama.")
    
    streaming = not args.no_stream
    
    # Run based on mode
    if args.query:
        # Single query mode
        run_single_debug_query(system, args.query, stream=streaming)
    else:
        # Interactive mode
        run_debug_mode(system, stream=streaming)

if __name__ == "__main__":
    main()
