""" Question 2: (2 marks)
Write a Python NLTK program to split all punctuation into separate tokens.
Sample Output:
Original string:
Reset your password if you just can't remember your old one.
Split all punctuation into separate tokens:
['Reser', your', 'password", If. you', 'just', 'can', "'', Y, 'remember', your', 'old", 'one. ' """

import nltk
from nltk.tokenize import ToktokTokenizer

# Ensure you have the necessary nltk data
nltk.download('punkt')

def split_punctuation(text):
    # Initialize ToktokTokenizer
    tokenizer = ToktokTokenizer()
    # Tokenize the text
    tokens = tokenizer.tokenize(text)
    return tokens

# Example usage
text = "Reset your password if you just can't remember your old one."
print("Original string:")
print(text)
print("Split all punctuation into separate tokens:")
print(split_punctuation(text))
