"""Question 1: (2 marks)
Define a function percent(word, text) that calculates how often a given word occurs in a text
and expresses the result as a percentage."""

import nltk
from nltk.tokenize import word_tokenize

def percent(word, text):
    # Tokenize the text
    tokens = word_tokenize(text.lower())
    # Count occurrences of the word
    word_count = tokens.count(word.lower())
    # Calculate the percentage
    total_words = len(tokens)
    percentage = (word_count / total_words) * 100
    return percentage

# Example usage
text = "I want to pass the PE test"
word = "test"
print(f"The word '{word}' occurs {percent(word, text):.2f}% of the time in the given text.")