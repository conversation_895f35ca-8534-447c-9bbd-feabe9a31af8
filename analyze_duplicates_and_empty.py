#!/usr/bin/env python3
"""
Comprehensive analysis tool to detect duplicate questions and empty answers
"""

from nlp_qa_system import NLPQASystem
from collections import defaultdict
import re

def analyze_duplicates_and_empty():
    """Analyze the database for duplicates and empty answers."""
    print("🔍 Loading NLP QA System for duplicate and empty answer analysis...")
    
    # Initialize system
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    print(f"✅ Loaded {len(system.qa_pairs)} questions from database")
    
    # Analyze duplicates
    print(f"\n{'='*80}")
    print("🔍 DUPLICATE ANALYSIS")
    print(f"{'='*80}")
    
    analyze_duplicate_titles(system.qa_pairs)
    analyze_duplicate_content(system.qa_pairs)
    analyze_duplicate_numbers(system.qa_pairs)
    
    # Analyze empty answers
    print(f"\n{'='*80}")
    print("📝 EMPTY ANSWER ANALYSIS")
    print(f"{'='*80}")
    
    analyze_empty_answers(system.qa_pairs)
    analyze_short_answers(system.qa_pairs)
    analyze_comment_only_answers(system.qa_pairs)

def analyze_duplicate_titles(qa_pairs):
    """Check for duplicate question titles."""
    title_groups = defaultdict(list)
    
    for qa_pair in qa_pairs:
        # Normalize title for comparison
        normalized_title = normalize_text(qa_pair.question_title)
        title_groups[normalized_title].append(qa_pair)
    
    duplicates = {title: pairs for title, pairs in title_groups.items() if len(pairs) > 1}
    
    print(f"📋 DUPLICATE TITLES ANALYSIS:")
    print(f"   Total unique titles: {len(title_groups)}")
    print(f"   Duplicate title groups: {len(duplicates)}")
    
    if duplicates:
        print(f"\n🚨 Found {len(duplicates)} groups of questions with duplicate titles:")
        for i, (title, pairs) in enumerate(duplicates.items(), 1):
            print(f"\n[{i}] Title: \"{title[:80]}{'...' if len(title) > 80 else ''}\"")
            print(f"    Questions: {len(pairs)}")
            for pair in pairs:
                print(f"    - Q{pair.question_number} ({pair.question_type}) from {pair.source_file}")
    else:
        print("✅ No duplicate titles found!")

def analyze_duplicate_content(qa_pairs):
    """Check for duplicate question content."""
    content_groups = defaultdict(list)
    
    for qa_pair in qa_pairs:
        # Normalize content for comparison (first 200 chars of answer code)
        normalized_content = normalize_text(qa_pair.answer_code[:200])
        if normalized_content:  # Only check non-empty content
            content_groups[normalized_content].append(qa_pair)
    
    duplicates = {content: pairs for content, pairs in content_groups.items() if len(pairs) > 1}
    
    print(f"\n📄 DUPLICATE CONTENT ANALYSIS:")
    print(f"   Total unique content signatures: {len(content_groups)}")
    print(f"   Duplicate content groups: {len(duplicates)}")
    
    if duplicates:
        print(f"\n🚨 Found {len(duplicates)} groups of questions with similar content:")
        for i, (content, pairs) in enumerate(list(duplicates.items())[:5], 1):  # Show first 5
            print(f"\n[{i}] Content signature: \"{content[:60]}{'...' if len(content) > 60 else ''}\"")
            print(f"    Questions: {len(pairs)}")
            for pair in pairs:
                print(f"    - Q{pair.question_number}: {pair.question_title[:50]}{'...' if len(pair.question_title) > 50 else ''}")
        
        if len(duplicates) > 5:
            print(f"\n... and {len(duplicates) - 5} more duplicate content groups")
    else:
        print("✅ No duplicate content found!")

def analyze_duplicate_numbers(qa_pairs):
    """Check for duplicate question numbers."""
    number_groups = defaultdict(list)
    
    for qa_pair in qa_pairs:
        number_groups[qa_pair.question_number].append(qa_pair)
    
    duplicates = {num: pairs for num, pairs in number_groups.items() if len(pairs) > 1}
    
    print(f"\n🔢 DUPLICATE NUMBERS ANALYSIS:")
    print(f"   Total unique numbers: {len(number_groups)}")
    print(f"   Duplicate number groups: {len(duplicates)}")
    
    if duplicates:
        print(f"\n🚨 Found {len(duplicates)} duplicate question numbers:")
        for num, pairs in duplicates.items():
            print(f"\n   Question Number {num}: {len(pairs)} questions")
            for pair in pairs:
                print(f"   - {pair.question_title[:60]}{'...' if len(pair.question_title) > 60 else ''} ({pair.question_type})")
    else:
        print("✅ No duplicate question numbers found!")

def analyze_empty_answers(qa_pairs):
    """Check for completely empty answers."""
    empty_answers = []
    
    for qa_pair in qa_pairs:
        if not qa_pair.answer_code.strip():
            empty_answers.append(qa_pair)
    
    print(f"📝 EMPTY ANSWERS ANALYSIS:")
    print(f"   Total questions: {len(qa_pairs)}")
    print(f"   Empty answers: {len(empty_answers)}")
    
    if empty_answers:
        print(f"\n🚨 Found {len(empty_answers)} questions with completely empty answers:")
        for qa_pair in empty_answers:
            print(f"   - Q{qa_pair.question_number}: {qa_pair.question_title} ({qa_pair.question_type})")
    else:
        print("✅ No completely empty answers found!")

def analyze_short_answers(qa_pairs):
    """Check for very short answers."""
    short_answers = []
    
    for qa_pair in qa_pairs:
        code_length = len(qa_pair.answer_code.strip())
        if 0 < code_length < 20:  # Very short but not empty
            short_answers.append((qa_pair, code_length))
    
    print(f"\n📏 SHORT ANSWERS ANALYSIS:")
    print(f"   Questions with very short answers (<20 chars): {len(short_answers)}")
    
    if short_answers:
        print(f"\n⚠️ Found {len(short_answers)} questions with very short answers:")
        for qa_pair, length in sorted(short_answers, key=lambda x: x[1]):
            print(f"   - Q{qa_pair.question_number} ({length} chars): {qa_pair.question_title[:50]}{'...' if len(qa_pair.question_title) > 50 else ''}")
            print(f"     Answer: \"{qa_pair.answer_code.strip()}\"")
    else:
        print("✅ No very short answers found!")

def analyze_comment_only_answers(qa_pairs):
    """Check for answers that are only comments."""
    comment_only = []
    
    for qa_pair in qa_pairs:
        code = qa_pair.answer_code.strip()
        if code:
            # Check if all non-empty lines are comments
            lines = [line.strip() for line in code.split('\n') if line.strip()]
            if lines and all(line.startswith('#') for line in lines):
                comment_only.append(qa_pair)
    
    print(f"\n💬 COMMENT-ONLY ANSWERS ANALYSIS:")
    print(f"   Questions with comment-only answers: {len(comment_only)}")
    
    if comment_only:
        print(f"\n⚠️ Found {len(comment_only)} questions with only commented code:")
        for qa_pair in comment_only[:10]:  # Show first 10
            print(f"   - Q{qa_pair.question_number}: {qa_pair.question_title[:50]}{'...' if len(qa_pair.question_title) > 50 else ''}")
            print(f"     First line: \"{qa_pair.answer_code.split(chr(10))[0][:60]}{'...' if len(qa_pair.answer_code.split(chr(10))[0]) > 60 else ''}\"")
        
        if len(comment_only) > 10:
            print(f"   ... and {len(comment_only) - 10} more comment-only answers")
    else:
        print("✅ No comment-only answers found!")

def normalize_text(text):
    """Normalize text for comparison."""
    if not text:
        return ""
    
    # Convert to lowercase, remove extra whitespace, remove special characters
    normalized = re.sub(r'[^\w\s]', '', text.lower())
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    return normalized

def show_question_statistics(qa_pairs):
    """Show general statistics about the questions."""
    print(f"\n{'='*80}")
    print("📊 GENERAL STATISTICS")
    print(f"{'='*80}")
    
    # Count by type
    type_counts = defaultdict(int)
    source_counts = defaultdict(int)
    
    total_code_length = 0
    code_lengths = []
    
    for qa_pair in qa_pairs:
        type_counts[qa_pair.question_type] += 1
        source_counts[qa_pair.source_file] += 1
        
        code_length = len(qa_pair.answer_code)
        total_code_length += code_length
        code_lengths.append(code_length)
    
    print(f"📋 Question Types:")
    for q_type, count in sorted(type_counts.items()):
        print(f"   {q_type}: {count} questions")
    
    print(f"\n📁 Source Files:")
    for source, count in sorted(source_counts.items()):
        print(f"   {source}: {count} questions")
    
    print(f"\n📏 Code Length Statistics:")
    if code_lengths:
        print(f"   Average code length: {total_code_length / len(code_lengths):.1f} characters")
        print(f"   Shortest answer: {min(code_lengths)} characters")
        print(f"   Longest answer: {max(code_lengths)} characters")
        print(f"   Median length: {sorted(code_lengths)[len(code_lengths)//2]} characters")

def main():
    """Main function."""
    print("="*80)
    print("🔍 NLP QA Database - Duplicate & Empty Answer Analysis")
    print("="*80)
    
    analyze_duplicates_and_empty()
    
    # Load system again for statistics
    system = NLPQASystem()
    if system.initialize_system():
        show_question_statistics(system.qa_pairs)
    
    print(f"\n{'='*80}")
    print("✅ Analysis Complete!")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
