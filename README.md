# NLP Question-Answer System with Qwen2.5 and ChromaDB

A comprehensive system that indexes NLP question-answer pairs from coursework AND previous exam questions, providing semantic search with AI-powered responses using Qwen2.5 (4B) local LLM and ChromaDB vector database.

## Features

- 🔍 **Semantic Search**: Find relevant Q&A pairs using ChromaDB vector embeddings
- 🤖 **AI Responses**: Get intelligent answers using Qwen2.5:4b local LLM
- 📚 **Complete Q&A Pairs**: Search results maintain question-answer integrity (no chunking)
- 📝 **Coursework + Exams**: Indexes both NLP coursework AND previous exam questions (83+ total)
- 🏷️ **Source Tracking**: Shows whether results come from coursework or exam questions
- 🚀 **Easy Setup**: Automated installation and configuration scripts
- 💬 **Interactive CLI**: User-friendly command-line interface
- 🔧 **Flexible**: Support for different Ollama models and custom NLP files

## Quick Start

### 1. Setup

```bash
# Install dependencies and configure the system
python setup_nlp_qa_system.py
```

### 2. Install Ollama and Qwen2.5:4b Model

Visit [https://ollama.ai/](https://ollama.ai/) and install Ollama for your operating system.

```bash
# Pull the Qwen2.5:4b model (recommended for NLP tasks)
ollama pull qwen2.5:4b

# Or use the automated installer
python install_qwen.py

# Start Ollama service (usually starts automatically)
ollama serve
```

### 3. Run the System

```bash
# Interactive mode
python run_nlp_qa.py

# Single query
python run_nlp_qa.py -q "How to tokenize text with NLTK?"

# Use custom file
python run_nlp_qa.py --file my_nlp_questions.py
```

## System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   NLP_Coursera  │───▶│  Question-Answer │───▶│    ChromaDB     │
│      .py        │    │     Parser       │    │  Vector Store   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐             │
│     User        │◄──▶│   NLP QA System  │◄────────────┘
│   Interface     │    │   (Main Logic)   │
└─────────────────┘    └──────────────────┘
                                │
                       ┌──────────────────┐
                       │  Ollama Client   │
                       │   (Local LLM)    │
                       └──────────────────┘
```

## File Structure

```
NLP301c/
├── nlp_qa_system.py          # Main system implementation
├── run_nlp_qa.py             # CLI interface
├── setup_nlp_qa_system.py    # Setup and installation script
├── NLP_Coursera.py           # Source Q&A pairs (your file)
├── requirements.txt          # Python dependencies
├── README.md                 # This file
├── chroma_db/               # ChromaDB storage (created automatically)
└── logs/                    # Log files (created automatically)
```

## Usage Examples

### Interactive Mode

```bash
python run_nlp_qa.py
```

```
🤖 Your question: How do I remove stopwords?

🔍 Searching for relevant information...

📋 RELEVANT QUESTION-ANSWER PAIRS:
============================================================

[1] 📝 Question 7: How to remove stop words in a text ?
🎯 Relevance: 95.2%
📖 Description: Remove all the stopwords ( 'a' , 'the', 'was'…) from the text
💻 Code Solution:
  from nltk.corpus import stopwords
  import nltk
  my_stopwords = set(stopwords.words('english'))
  # ... (code continues)

🤖 AI ASSISTANT RESPONSE:
============================================================
To remove stopwords from text, you can use NLTK's built-in stopwords corpus...
```

### Single Query Mode

```bash
python run_nlp_qa.py -q "How to perform stemming?"
```

### Custom Options

```bash
# Use different Ollama model
python run_nlp_qa.py --model llama2

# Get more search results
python run_nlp_qa.py --results 5

# Use custom NLP file (without exam questions)
python run_nlp_qa.py --file my_custom_nlp.py
```

## System Components

### 1. NLPFileParser
- Parses the `NLP_Coursera.py` file
- Extracts question-answer pairs maintaining their structure
- Handles numbered questions with titles, descriptions, and code solutions

### 2. ChromaDBManager
- Manages vector database operations
- Indexes complete Q&A pairs (not chunks)
- Provides semantic search functionality
- Persists data locally in `chroma_db/` directory

### 3. OllamaClient
- Interfaces with local Ollama LLM service
- Generates contextual responses using search results
- Handles connection errors gracefully

### 4. NLPQASystem
- Main orchestrator combining all components
- Processes user queries end-to-end
- Returns structured results with search results and AI responses

## Configuration

### Environment Variables

```bash
# Optional: Set custom Ollama URL
export OLLAMA_URL="http://localhost:11434"

# Optional: Set default model
export OLLAMA_MODEL="llama2"
```

### Custom Models

The system supports any Ollama model. Popular options:

- `qwen2.5:4b` - Excellent for NLP tasks, fast and efficient (default)
- `llama2` - General purpose
- `codellama` - Better for code-related questions
- `mistral` - Fast and efficient
- `neural-chat` - Conversational AI

## Troubleshooting

### Common Issues

1. **"Ollama service not available"**
   ```bash
   # Check if Ollama is running
   curl http://localhost:11434/api/tags
   
   # Start Ollama if needed
   ollama serve
   ```

2. **"No QA pairs found"**
   - Ensure `NLP_Coursera.py` is in the current directory
   - Check file format matches expected structure

3. **"ChromaDB errors"**
   ```bash
   # Clear ChromaDB and reinitialize
   rm -rf chroma_db/
   python nlp_qa_system.py
   ```

4. **"Import errors"**
   ```bash
   # Reinstall requirements
   pip install -r requirements.txt
   ```

### Debug Mode

```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Advanced Usage

### Programmatic API

```python
from nlp_qa_system import NLPQASystem

# Initialize system
system = NLPQASystem("NLP_Coursera.py", ollama_model="codellama")
system.initialize_system()

# Ask questions
result = system.ask_question("How to tokenize text?", n_results=5)

# Access results
for search_result in result['search_results']:
    print(f"Q{search_result['metadata']['question_number']}: {search_result['metadata']['question_title']}")

print(f"AI Response: {result['llm_response']}")
```

### Custom File Format

The system expects files with this structure:

```python
# =====================================================================================================================
# 1. Question Title
# Q. Question description
# code here
# more code

# =====================================================================================================================
# 2. Another Question Title
# Q. Another question description
# solution code
```

## Performance

- **Indexing**: ~1-2 seconds for 50 Q&A pairs
- **Search**: ~100-200ms per query
- **LLM Response**: 2-10 seconds (depends on model and complexity)
- **Memory**: ~100-500MB (depends on model size)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source. Feel free to use and modify as needed.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs in `logs/` directory
3. Create an issue with detailed error information
