""""Question 2: (2 marks)
Write a program to extract and print all non-noun words present in the below text.
Hint: using spacy package
Example:
Input:
"<PERSON> works at Microsoft. She lives in Manchester and likes to play the flute."
Output:
works
at
She
lives
in
and
likes
to
olay
the"""

import spacy

def extract_non_noun_words(text):
  """Extracts and prints all non-noun words from the given text.

  Args:
    text: The input text.
  """

  nlp = spacy.load("en_core_web_sm")  # Load the English language model
  doc = nlp(text)

  non_noun_words = []
  for token in doc:
    if token.pos_ != "NOUN" and token.pos_ != "PROPN":  # Exclude nouns and proper nouns
      non_noun_words.append(token.text)

  print("Non-noun words:", " ".join(non_noun_words))

if __name__ == "__main__":
  text = "<PERSON> works at Microsoft. She lives in Manchester and likes to play the flute."
  extract_non_noun_words(text)