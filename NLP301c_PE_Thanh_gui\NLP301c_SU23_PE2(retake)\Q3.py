"""Question 3: (3 marks)
Write a program to find all words with number of the letters less than 4 in the given text.
With the help of a frequency distribution (FreqDist), show these words in decreasing order of
frequency.
Example:
Input:
'He would also attend the opening ceremony for the construction of the U.S.
Embassy complex in Cau Giay District, as well as meeting students, teachers and
scientists at the Hanoi University of Science and Technology'
Output:
['Cau', 'He' ,'and' , 'as' , 'at' ,'for' , 'in', 'of' , 'the']
"""

import nltk
from nltk.tokenize import word_tokenize
from nltk.probability import FreqDist
import matplotlib.pyplot as plt


def words_less_than_four(text):
    # Tokenize the text
    tokens = word_tokenize(text)
    # Filter words with less than 4 letters and remove punctuation
    short_words = [word for word in tokens if len(word) < 4 and word.isalpha()]
    # Calculate the frequency distribution
    fdist = FreqDist(short_words)
    # Sort words by frequency in decreasing order
    sorted_words = sorted(fdist.items(), key=lambda item: item[1], reverse=True)
    # Extract only the words from the sorted list of tuples
    sorted_words_list = [word for word, freq in sorted_words]
    return sorted_words_list, fdist

# Example usage
text = (
    'He would also attend the opening ceremony for the construction of the U.S. '
    'Embassy complex in Cau Giay District, as well as meeting students, teachers and '
    'scientists at the Hanoi University of Science and Technology'
)
sorted_words, fdist = words_less_than_four(text)

print("Words with fewer than 4 letters in decreasing order of frequency:")
print(sorted_words)

# Plot the frequency distribution
fdist.plot(30, cumulative=False)
plt.show()
