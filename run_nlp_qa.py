#!/usr/bin/env python3
"""
Simple CLI runner for the NLP Question-Answer System
"""

import argparse
import sys
from pathlib import Path
from nlp_qa_system import NLPQASystem

def run_interactive_mode(system):
    """Run the system in interactive mode."""
    print("\n" + "="*60)
    print("NLP Question-Answer System - Interactive Mode")
    print("="*60)
    print("Ask questions about NLP concepts and get answers!")
    print("Commands:")
    print("  'quit' or 'exit' - Exit the system")
    print("  'list' - Show available questions")
    print("  'help' - Show this help message")
    print("="*60)
    
    while True:
        try:
            user_input = input("\n🤖 Your question: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if user_input.lower() in ['help', 'h']:
                print("\nCommands:")
                print("  'quit' or 'exit' - Exit the system")
                print("  'list' - Show available questions")
                print("  'help' - Show this help message")
                continue
            
            if user_input.lower() == 'list':
                print("\n📚 Available Questions (first 15):")
                print("-" * 50)
                for qa_pair in system.qa_pairs[:15]:
                    print(f"{qa_pair.question_number:2d}. {qa_pair.question_title}")
                if len(system.qa_pairs) > 15:
                    print(f"... and {len(system.qa_pairs) - 15} more questions")
                continue
            
            if not user_input:
                continue
            
            # Process the question
            print("\n🔍 Searching for relevant information...")
            result = system.ask_question(user_input)

            # Show progress for AI generation
            if not result['llm_response'].startswith("Error"):
                print("🤖 Generating AI response (please wait, this may take a while)...")
            
            print(f"\n{'='*60}")
            print("📋 RELEVANT QUESTION-ANSWER PAIRS:")
            print(f"{'='*60}")
            
            for i, search_result in enumerate(result['search_results'], 1):
                metadata = search_result['metadata']
                relevance = 1 - search_result['distance']
                
                # Show question type and source
                question_type = metadata.get('question_type', 'coursework')
                source_info = f"({question_type})"
                if metadata.get('source_file'):
                    import os
                    source_file = os.path.basename(metadata['source_file'])
                    source_info = f"({question_type} - {source_file})"

                print(f"\n[{i}] 📝 Question {metadata['question_number']}: {metadata['question_title']} {source_info}")
                print(f"🎯 Relevance: {relevance:.1%}")

                if metadata.get('question_description'):
                    print(f"📖 Description: {metadata['question_description']}")
                
                print("💻 Code Solution:")
                print("-" * 30)
                
                # Extract and display the code part
                content_lines = search_result['content'].split('\n')
                in_solution = False
                code_lines = []
                
                for line in content_lines:
                    if line.startswith('Solution:'):
                        in_solution = True
                        continue
                    if in_solution:
                        code_lines.append(line)
                
                # Show first 10 lines of code
                for line in code_lines[:10]:
                    if line.strip():
                        print(f"  {line}")
                
                if len(code_lines) > 10:
                    print("  ... (truncated)")
                
                print()
            
            print(f"{'='*60}")
            print("🤖 AI ASSISTANT RESPONSE:")
            print(f"{'='*60}")
            if result['llm_response'].startswith("Error"):
                print(f"❌ {result['llm_response']}")
            else:
                print(result['llm_response'])
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def run_single_query(system, query):
    """Run a single query and return the result."""
    result = system.ask_question(query)
    
    print(f"Question: {query}")
    print("="*50)
    
    print("\nRelevant Q&A Pairs:")
    for i, search_result in enumerate(result['search_results'], 1):
        metadata = search_result['metadata']
        relevance = 1 - search_result['distance']
        question_type = metadata.get('question_type', 'coursework')
        print(f"{i}. Q{metadata['question_number']}: {metadata['question_title']} ({question_type}) (Relevance: {relevance:.1%})")
    
    print(f"\nAI Response:\n{result['llm_response']}")

def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(
        description="NLP Question-Answer System with Ollama and ChromaDB",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_nlp_qa.py                           # Interactive mode
  python run_nlp_qa.py -q "How to tokenize text?" # Single query
  python run_nlp_qa.py --file custom_nlp.py      # Use custom file
        """
    )
    
    parser.add_argument(
        '-q', '--query',
        type=str,
        help='Ask a single question and exit'
    )
    
    parser.add_argument(
        '--file',
        type=str,
        default='NLP_Coursera.py',
        help='Path to the NLP coursework file (default: NLP_Coursera.py)'
    )
    
    parser.add_argument(
        '--model',
        type=str,
        default='qwen3:4b',
        help='Ollama model to use (default: qwen2.5:4b)'
    )
    
    parser.add_argument(
        '--results',
        type=int,
        default=3,
        help='Number of search results to return (default: 3)'
    )
    
    args = parser.parse_args()
    
    # Check if the NLP file exists
    if not Path(args.file).exists():
        print(f"❌ Error: File '{args.file}' not found.")
        print("Please ensure the NLP coursework file is in the current directory.")
        sys.exit(1)
    
    # Initialize system with coursework and exam questions
    print("🚀 Initializing NLP QA System with coursework and exam questions...")

    # Check if using default file or custom file
    if args.file == 'NLP_Coursera.py':
        # Use default setup with both coursework and exams
        system = NLPQASystem(ollama_model=args.model)
    else:
        # Use custom file only
        system = NLPQASystem(nlp_file_paths=[args.file], exam_directories=[], ollama_model=args.model)
    
    if not system.initialize_system():
        print("❌ Failed to initialize system. Please check the setup.")
        sys.exit(1)
    
    print(f"✅ System ready! Loaded {len(system.qa_pairs)} question-answer pairs.")
    
    # Check Ollama availability
    if not system.ollama_client.is_available():
        print("⚠️  Warning: Ollama service not available.")
        print("   You'll still get search results, but no AI-generated responses.")
        print("   To enable AI responses, please install and start Ollama.")
    
    # Run based on mode
    if args.query:
        # Single query mode
        run_single_query(system, args.query)
    else:
        # Interactive mode
        run_interactive_mode(system)

if __name__ == "__main__":
    main()
