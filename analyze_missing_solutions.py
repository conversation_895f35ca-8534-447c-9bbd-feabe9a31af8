#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze and find questions without solutions in the database
"""

from nlp_qa_system import NLPQASystem
import re

def analyze_qa_pairs():
    """Analyze all Q&A pairs to find those without solutions."""
    print("🔍 Analyzing Q&A pairs for missing solutions...")
    
    # Initialize system
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    print(f"✅ Loaded {len(system.qa_pairs)} Q&A pairs")
    
    # Analyze each Q&A pair
    missing_solutions = []
    empty_solutions = []
    short_solutions = []
    
    for qa_pair in system.qa_pairs:
        solution_length = len(qa_pair.answer_code.strip())
        
        if solution_length == 0:
            missing_solutions.append(qa_pair)
        elif solution_length < 20:  # Very short solutions
            short_solutions.append(qa_pair)
        elif not has_meaningful_code(qa_pair.answer_code):
            empty_solutions.append(qa_pair)
    
    # Report findings
    print(f"\n{'='*80}")
    print("📊 ANALYSIS RESULTS")
    print(f"{'='*80}")
    print(f"Total Q&A pairs: {len(system.qa_pairs)}")
    print(f"Missing solutions (empty): {len(missing_solutions)}")
    print(f"Very short solutions (<20 chars): {len(short_solutions)}")
    print(f"No meaningful code: {len(empty_solutions)}")
    
    # Show details
    if missing_solutions:
        print(f"\n❌ QUESTIONS WITH NO SOLUTIONS:")
        print("-" * 50)
        for qa in missing_solutions:
            print(f"Q{qa.question_number}: {qa.question_title} ({qa.question_type})")
            if qa.question_description:
                print(f"   Description: {qa.question_description}")
            print(f"   Source: {qa.source_file}")
            print()
    
    if short_solutions:
        print(f"\n⚠️ QUESTIONS WITH VERY SHORT SOLUTIONS:")
        print("-" * 50)
        for qa in short_solutions[:10]:  # Show first 10
            print(f"Q{qa.question_number}: {qa.question_title} ({qa.question_type})")
            print(f"   Solution length: {len(qa.answer_code.strip())} characters")
            print(f"   Solution: {qa.answer_code.strip()[:50]}...")
            print()
    
    if empty_solutions:
        print(f"\n⚠️ QUESTIONS WITH NO MEANINGFUL CODE:")
        print("-" * 50)
        for qa in empty_solutions[:10]:  # Show first 10
            print(f"Q{qa.question_number}: {qa.question_title} ({qa.question_type})")
            print(f"   Solution: {qa.answer_code.strip()[:100]}...")
            print()
    
    return missing_solutions, short_solutions, empty_solutions

def has_meaningful_code(code):
    """Check if the code contains meaningful programming content."""
    code = code.strip()
    
    if not code:
        return False
    
    # Check for common programming patterns
    programming_patterns = [
        r'import\s+\w+',           # import statements
        r'from\s+\w+\s+import',    # from import statements
        r'def\s+\w+\(',            # function definitions
        r'class\s+\w+',            # class definitions
        r'=\s*[^=]',               # assignments (not ==)
        r'if\s+.*:',               # if statements
        r'for\s+.*:',              # for loops
        r'while\s+.*:',            # while loops
        r'print\s*\(',             # print statements
        r'\w+\.\w+\(',             # method calls
        r'\[.*\]',                 # list/array access
    ]
    
    for pattern in programming_patterns:
        if re.search(pattern, code):
            return True
    
    # Check if it's just comments
    lines = code.split('\n')
    non_comment_lines = [line for line in lines if line.strip() and not line.strip().startswith('#')]
    
    return len(non_comment_lines) > 0

def fix_missing_solutions():
    """Suggest fixes for questions without solutions."""
    print(f"\n{'='*80}")
    print("🔧 SUGGESTED FIXES")
    print(f"{'='*80}")
    
    print("1. For questions without solutions:")
    print("   - Add placeholder solutions")
    print("   - Mark as 'Solution not provided'")
    print("   - Exclude from database if not useful")
    
    print("\n2. For questions with short solutions:")
    print("   - Review if they are complete")
    print("   - Add more context if needed")
    
    print("\n3. For questions with no meaningful code:")
    print("   - Check if they are theoretical questions")
    print("   - Add example code if applicable")
    print("   - Mark as concept-only questions")

def create_filtered_system():
    """Create a version that filters out questions without meaningful solutions."""
    print(f"\n{'='*80}")
    print("🔧 CREATING FILTERED SYSTEM")
    print(f"{'='*80}")
    
    # This would modify the parser to skip questions without solutions
    print("To implement filtering:")
    print("1. Modify _parse_coursework_section to check solution quality")
    print("2. Modify _parse_exam_file to validate solutions")
    print("3. Add a 'has_solution' flag to QAPair dataclass")
    print("4. Filter during indexing or search")

def main():
    """Main analysis function."""
    print("="*80)
    print("🔍 Missing Solutions Analysis for NLP QA System")
    print("="*80)
    
    missing, short, empty = analyze_qa_pairs()
    
    fix_missing_solutions()
    
    if missing or short or empty:
        print(f"\n{'='*80}")
        print("⚠️ RECOMMENDATIONS")
        print(f"{'='*80}")
        
        if missing:
            print(f"• {len(missing)} questions have no solutions - consider excluding or adding placeholders")
        
        if short:
            print(f"• {len(short)} questions have very short solutions - review for completeness")
        
        if empty:
            print(f"• {len(empty)} questions have no meaningful code - check if they're theoretical")
        
        print("\nOptions:")
        print("1. Filter out questions without solutions during parsing")
        print("2. Add placeholder solutions for missing ones")
        print("3. Mark questions as 'concept-only' vs 'code-based'")
        print("4. Improve search to prefer questions with solutions")
    
    else:
        print(f"\n✅ All questions appear to have meaningful solutions!")

if __name__ == "__main__":
    main()
