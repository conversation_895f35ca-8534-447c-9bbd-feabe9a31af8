#!/usr/bin/env python3
"""
NLP Question-Answer System with Ollama and ChromaDB
This system indexes NLP question-answer pairs and provides semantic search with LLM responses.
"""

import re
import json
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import chromadb
from chromadb.config import Settings
import requests
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class QAPair:
    """Represents a question-answer pair from the NLP coursework or exams."""
    question_number: int
    question_title: str
    question_description: str
    answer_code: str
    full_content: str
    source_file: str = ""
    question_type: str = "coursework"  # "coursework" or "exam"

class NLPFileParser:
    """Parses NLP files to extract question-answer pairs from coursework and exams."""

    def __init__(self, file_paths: List[str] = None, exam_directories: List[str] = None):
        self.file_paths = file_paths or ["NLP_Coursera.py"]
        self.exam_directories = exam_directories or []
        
    def parse_file(self) -> List[QAPair]:
        """Parse all NLP files and extract question-answer pairs from coursework and exams."""
        all_qa_pairs = []

        # Parse coursework files
        for file_path in self.file_paths:
            qa_pairs = self._parse_coursework_file(file_path)
            all_qa_pairs.extend(qa_pairs)

        # Parse exam directories
        for exam_dir in self.exam_directories:
            qa_pairs = self._parse_exam_directory(exam_dir)
            all_qa_pairs.extend(qa_pairs)

        logger.info(f"Parsed {len(all_qa_pairs)} total question-answer pairs")
        return all_qa_pairs

    def _parse_coursework_file(self, file_path: str) -> List[QAPair]:
        """Parse a coursework file (like NLP_Coursera.py or Tonghop.py)."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()

            qa_pairs = []

            # Check if this is Tonghop.py with different format
            if "Tonghop.py" in file_path:
                qa_pairs = self._parse_tonghop_file(content, file_path)
            else:
                # Standard format with === separators
                sections = re.split(r'# ={50,}', content)

                for section in sections[1:]:  # Skip the first empty section
                    qa_pair = self._parse_coursework_section(section, file_path)
                    if qa_pair:
                        qa_pairs.append(qa_pair)

            logger.info(f"Parsed {len(qa_pairs)} coursework question-answer pairs from {file_path}")
            return qa_pairs

        except Exception as e:
            logger.error(f"Error parsing coursework file {file_path}: {e}")
            return []
    
    def _parse_exam_directory(self, exam_dir: str) -> List[QAPair]:
        """Parse exam questions from a directory containing Q1.py, Q2.py, etc."""
        qa_pairs = []

        try:
            from pathlib import Path
            exam_path = Path(exam_dir)

            if not exam_path.exists():
                logger.warning(f"Exam directory {exam_dir} does not exist")
                return []

            # Find all Q*.py files
            question_files = sorted(exam_path.glob("Q*.py"))

            for question_file in question_files:
                qa_pair = self._parse_exam_file(str(question_file), exam_dir)
                if qa_pair:
                    qa_pairs.append(qa_pair)

            logger.info(f"Parsed {len(qa_pairs)} exam questions from {exam_dir}")
            return qa_pairs

        except Exception as e:
            logger.error(f"Error parsing exam directory {exam_dir}: {e}")
            return []

    def _parse_exam_file(self, file_path: str, exam_dir: str) -> Optional[QAPair]:
        """Parse a single exam question file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()

            from pathlib import Path
            file_name = Path(file_path).stem  # e.g., "Q1"

            # Extract question number from filename
            question_match = re.match(r'Q(\d+)', file_name)
            if not question_match:
                return None

            question_number = int(question_match.group(1))

            # Extract question description from comments
            lines = content.split('\n')
            question_description = ""
            question_title = f"Exam Question {question_number}"

            # Look for question description in comments
            for line in lines[:10]:  # Check first 10 lines
                line = line.strip()
                if line.startswith('#') and len(line) > 5:
                    # Remove # and clean up
                    desc_line = line[1:].strip()
                    if desc_line and not desc_line.startswith('Cau') and not desc_line.startswith('Question'):
                        if not question_description:
                            question_description = desc_line
                        else:
                            question_description += " " + desc_line
                    elif desc_line.startswith('Question') or desc_line.startswith('Cau'):
                        question_title = desc_line

            # Check if the solution has meaningful content
            if not self._has_meaningful_solution(content):
                logger.debug(f"Skipping exam Q{question_number} from {exam_dir} - no meaningful solution")
                return None

            # Create full content for embedding
            full_content = f"Exam Question {question_number}: {question_title}\n"
            if question_description:
                full_content += f"Description: {question_description}\n"
            full_content += f"Source: {exam_dir}\n"
            full_content += f"Solution:\n{content}"

            # Create unique question number based on file path and original number
            import hashlib
            unique_id = hashlib.md5(f"{exam_dir}_{file_name}".encode()).hexdigest()[:8]
            unique_question_number = 1000 + int(unique_id, 16) % 9000  # Ensure it's in 1000-9999 range

            return QAPair(
                question_number=unique_question_number,
                question_title=question_title,
                question_description=question_description,
                answer_code=content,
                full_content=full_content,
                source_file=file_path,
                question_type="exam"
            )

        except Exception as e:
            logger.error(f"Error parsing exam file {file_path}: {e}")
            return None

    def _has_meaningful_solution(self, code: str) -> bool:
        """Check if the code contains meaningful programming content."""
        code = code.strip()

        if not code or len(code) < 10:
            return False

        # Check for common programming patterns
        programming_patterns = [
            r'import\s+\w+',           # import statements
            r'from\s+\w+\s+import',    # from import statements
            r'def\s+\w+\(',            # function definitions
            r'class\s+\w+',            # class definitions
            r'=\s*[^=]',               # assignments (not ==)
            r'if\s+.*:',               # if statements
            r'for\s+.*:',              # for loops
            r'while\s+.*:',            # while loops
            r'print\s*\(',             # print statements
            r'\w+\.\w+\(',             # method calls
            r'\[.*\]',                 # list/array access
        ]

        for pattern in programming_patterns:
            if re.search(pattern, code):
                return True

        # Check if it's just comments
        lines = code.split('\n')
        non_comment_lines = [line for line in lines if line.strip() and not line.strip().startswith('#')]

        return len(non_comment_lines) > 2  # At least 3 lines of actual code

    def _parse_tonghop_file(self, content: str, file_path: str) -> List[QAPair]:
        """Parse Tonghop.py file with its specific format."""
        qa_pairs = []

        # Split by paper sections
        paper_sections = re.split(r'# #Paper_(\d+)', content)

        current_paper = None
        for i, section in enumerate(paper_sections):
            if i == 0:  # Skip the first empty section
                continue

            if i % 2 == 1:  # Odd indices are paper numbers
                current_paper = section.strip()
                continue

            # Even indices are paper content
            if current_paper:
                paper_qa_pairs = self._parse_tonghop_paper(section, current_paper, file_path)
                qa_pairs.extend(paper_qa_pairs)

        return qa_pairs

    def _parse_tonghop_paper(self, paper_content: str, paper_number: str, file_path: str) -> List[QAPair]:
        """Parse a single paper section from Tonghop.py."""
        qa_pairs = []

        # Split by question numbers
        question_sections = re.split(r'# #(\d+)\.\s*(.*)', paper_content)

        current_question_num = None
        current_question_title = None

        for i, section in enumerate(question_sections):
            if i == 0:  # Skip the first section
                continue

            if i % 3 == 1:  # Question number
                current_question_num = section.strip()
            elif i % 3 == 2:  # Question title
                current_question_title = section.strip()
            elif i % 3 == 0:  # Question content
                if current_question_num and current_question_title:
                    qa_pair = self._parse_tonghop_question(
                        section, current_question_num, current_question_title,
                        paper_number, file_path
                    )
                    if qa_pair:
                        qa_pairs.append(qa_pair)

        return qa_pairs

    def _parse_tonghop_question(self, content: str, question_num: str, question_title: str,
                               paper_number: str, file_path: str) -> Optional[QAPair]:
        """Parse a single question from Tonghop.py."""
        lines = content.strip().split('\n')

        # Extract code (remove # comments)
        code_lines = []
        for line in lines:
            if line.strip().startswith('# '):
                # Remove the '# ' prefix
                code_line = line.strip()[2:]
                if code_line:
                    code_lines.append(code_line)
            elif line.strip() and not line.strip().startswith('#'):
                # Uncommented code
                code_lines.append(line.strip())

        answer_code = '\n'.join(code_lines)

        # Check if the solution has meaningful content
        if not self._has_meaningful_solution(answer_code):
            return None

        # Create unique question number
        import hashlib
        unique_string = f"tonghop_paper{paper_number}_q{question_num}_{question_title[:30]}"
        unique_id = hashlib.md5(unique_string.encode()).hexdigest()[:8]
        question_number = 30000 + int(unique_id, 16) % 9000  # Range 30000-38999

        # Create full content for embedding
        full_content = f"Tonghop Paper {paper_number} Question {question_num}: {question_title}\n"
        full_content += f"Solution:\n{answer_code}"

        return QAPair(
            question_number=question_number,
            question_title=f"Paper {paper_number} Q{question_num}: {question_title}",
            question_description=question_title,
            answer_code=answer_code,
            full_content=full_content,
            source_file=file_path,
            question_type="tonghop"
        )

    def _parse_coursework_section(self, section: str, source_file: str) -> Optional[QAPair]:
        """Parse a single coursework section to extract question and answer."""
        lines = section.strip().split('\n')
        if not lines:
            return None

        # Extract question number and title from first line
        first_line = lines[0].strip('# ')
        if not first_line:
            return None

        # Parse question number and title - handle multiple formats
        # Regular format: "1. Question title"
        # Exam format: "sm22_1.1. Question description"
        # Unnumbered format: "Write a Python NLTK program..."
        regular_match = re.match(r'(\d+)\.\s*(.*)', first_line)
        exam_match = re.match(r'(sm\d+_\d+\.\d+)\.\s*(.*)', first_line)
        unnumbered_match = re.match(r'(Write a Python NLTK program.*)', first_line)

        if regular_match:
            question_number = int(regular_match.group(1))
            question_title = regular_match.group(2)
            question_type = "coursework"
        elif exam_match:
            # For exam questions, create a unique number from the exam code
            exam_code = exam_match.group(1)
            question_title = exam_match.group(2)
            question_type = "previous_exam"
            # Convert sm22_1.1 to a unique number more carefully
            import hashlib
            # Use both exam code and title to ensure uniqueness
            unique_string = f"{exam_code}_{question_title[:50]}"
            unique_id = hashlib.md5(unique_string.encode()).hexdigest()[:8]
            question_number = 22000 + int(unique_id, 16) % 8000  # Range 22000-29999
        elif unnumbered_match:
            # For unnumbered questions, create a unique number from the title
            question_title = unnumbered_match.group(1)
            question_type = "coursework"
            import hashlib
            # Include source file and position to ensure uniqueness
            unique_string = f"unnumbered_{source_file}_{question_title[:50]}_{len(section)}"
            unique_id = hashlib.md5(unique_string.encode()).hexdigest()[:8]
            question_number = 60000 + int(unique_id, 16) % 9000  # Range 60000-68999
        else:
            return None

        # Extract question description and code
        question_description = ""
        answer_code = ""

        if question_type == "previous_exam":
            # For exam questions, the description is often in the title
            # and code is commented out
            question_description = question_title
            code_lines = []

            for line in lines[1:]:
                original_line = line
                line = line.strip()

                # Skip empty lines and separator lines
                if not line or line.startswith('# ==='):
                    continue

                # For exam questions, code is commented out, so we need to extract it
                if line.startswith('#'):
                    # Remove the # and add to code
                    code_line = line[1:].strip()
                    if code_line:  # Don't add empty lines
                        code_lines.append(code_line)
                else:
                    # Uncommented code
                    code_lines.append(line)

            answer_code = '\n'.join(code_lines)
        else:
            # Regular coursework questions
            # Find the Q. line and code, but be more flexible
            code_lines = []
            found_q_line = False

            for line in lines[1:]:
                original_line = line
                line = line.strip()

                # Skip empty lines and separator lines
                if not line or line.startswith('# ==='):
                    continue

                if line.startswith('# Q.'):
                    question_description = line[4:].strip()
                    found_q_line = True
                elif line.startswith('#'):
                    # For coursework, include all commented code
                    code_line = line[1:].strip()  # Remove the #
                    if code_line:
                        code_lines.append(code_line)
                else:
                    # Uncommented code
                    code_lines.append(line)

            # If no Q. line found, use the title as description
            if not found_q_line and not question_description:
                question_description = question_title

            answer_code = '\n'.join(code_lines)

        # For NLP_Coursera.py, include all questions (even if they have minimal code)
        # For other files, apply stricter filtering
        if "NLP_Coursera.py" not in source_file:
            if not self._has_meaningful_solution(answer_code):
                logger.debug(f"Skipping Q{question_number} ({question_type}) - no meaningful solution")
                return None
        else:
            # For NLP_Coursera.py, only skip completely empty questions
            if not answer_code.strip():
                logger.debug(f"Skipping Q{question_number} ({question_type}) - completely empty")
                return None

        # Create full content for embedding
        if question_type == "previous_exam":
            full_content = f"Previous Exam Question {question_number}: {question_title}\n"
        else:
            full_content = f"Question {question_number}: {question_title}\n"

        if question_description and question_description != question_title:
            full_content += f"Description: {question_description}\n"
        full_content += f"Solution:\n{answer_code}"

        return QAPair(
            question_number=question_number,
            question_title=question_title,
            question_description=question_description,
            answer_code=answer_code,
            full_content=full_content,
            source_file=source_file,
            question_type=question_type
        )

class ChromaDBManager:
    """Manages ChromaDB operations for storing and retrieving QA pairs."""

    def __init__(self, collection_name: str = "nlp_qa_pairs"):
        try:
            # Try new ChromaDB client initialization
            self.client = chromadb.PersistentClient(path="./chroma_db")
        except:
            try:
                # Fallback to older initialization method
                self.client = chromadb.Client(Settings(
                    chroma_db_impl="duckdb+parquet",
                    persist_directory="./chroma_db"
                ))
            except:
                # Last resort - in-memory client
                self.client = chromadb.Client()

        self.collection_name = collection_name
        self.collection = None
        
    def initialize_collection(self):
        """Initialize or get the ChromaDB collection."""
        try:
            # Try to get existing collection
            self.collection = self.client.get_collection(name=self.collection_name)
            logger.info(f"Loaded existing collection: {self.collection_name}")
        except:
            # Create new collection if it doesn't exist
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "NLP Question-Answer pairs for semantic search"}
            )
            logger.info(f"Created new collection: {self.collection_name}")
    
    def index_qa_pairs(self, qa_pairs: List[QAPair]):
        """Index question-answer pairs in ChromaDB."""
        if not self.collection:
            self.initialize_collection()
        
        documents = []
        metadatas = []
        ids = []
        
        for qa_pair in qa_pairs:
            # Use full content for embedding to maintain Q&A pair integrity
            documents.append(qa_pair.full_content)
            metadatas.append({
                "question_number": qa_pair.question_number,
                "question_title": qa_pair.question_title,
                "question_description": qa_pair.question_description,
                "source_file": qa_pair.source_file,
                "question_type": qa_pair.question_type,
                "type": "qa_pair"
            })
            ids.append(f"qa_pair_{qa_pair.question_number}")
        
        try:
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            logger.info(f"Indexed {len(qa_pairs)} QA pairs in ChromaDB")
        except Exception as e:
            logger.error(f"Error indexing QA pairs: {e}")
    
    def search_qa_pairs(self, query: str, n_results: int = 5) -> List[Dict]:
        """Search for relevant QA pairs based on query."""
        if not self.collection:
            self.initialize_collection()
        
        try:
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                include=["documents", "metadatas", "distances"]
            )
            
            search_results = []
            for i in range(len(results['documents'][0])):
                search_results.append({
                    "content": results['documents'][0][i],
                    "metadata": results['metadatas'][0][i],
                    "distance": results['distances'][0][i]
                })
            
            return search_results
        except Exception as e:
            logger.error(f"Error searching QA pairs: {e}")
            return []

class OllamaClient:
    """Client for interacting with Ollama local LLM."""

    def __init__(self, base_url: str = "http://localhost:11434", model: str = "qwen2.5:4b"):
        self.base_url = base_url
        self.model = model
    
    def is_available(self) -> bool:
        """Check if Ollama service is available."""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def generate_response(self, prompt: str, context: List[Dict] = None, debug: bool = False, stream: bool = False) -> str:
        """Generate response using Ollama LLM."""
        if not self.is_available():
            return "Error: Ollama service is not available. Please ensure Ollama is running."

        # Build context from search results
        context_text = ""
        if context:
            context_text = "\n\nRelevant NLP Question-Answer pairs:\n"
            for i, result in enumerate(context, 1):
                context_text += f"\n--- Result {i} ---\n"
                context_text += result['content']
                context_text += f"\n(Relevance score: {1 - result['distance']:.3f})\n"

        full_prompt = f"""You are an expert NLP assistant. Answer the user's question based on the provided NLP question-answer pairs from a coursework collection.

User Question: {prompt}
{context_text}

Please provide a comprehensive answer that:
1. Directly addresses the user's question
2. No explanation needed, just show the code
3. Include complete, working code examples from the Q&A pairs
4. Focus on practical implementation

Answer:"""

        # Debug: Show the full prompt
        if debug:
            print("\n" + "="*80)
            print("🔍 FULL SYSTEM PROMPT SENT TO AI:")
            print("="*80)
            print(full_prompt)
            print("="*80)
            print(f"📊 Prompt length: {len(full_prompt)} characters")
            print(f"🤖 Model: {self.model}")
            print("="*80)
        
        try:
            logger.info(f"Generating response with {self.model} (this may take a while)...")

            if stream:
                # Streaming response
                print(f"\n🤖 AI Response (streaming from {self.model}):")
                print("-" * 60)

                response = requests.post(
                    f"{self.base_url}/api/generate",
                    json={
                        "model": self.model,
                        "prompt": full_prompt,
                        "stream": True
                    },
                    stream=True
                    # No timeout - wait indefinitely for AI response
                )

                if response.status_code == 200:
                    full_response = ""
                    for line in response.iter_lines():
                        if line:
                            try:
                                chunk = json.loads(line.decode('utf-8'))
                                if 'response' in chunk:
                                    token = chunk['response']
                                    print(token, end='', flush=True)
                                    full_response += token
                                if chunk.get('done', False):
                                    break
                            except json.JSONDecodeError:
                                continue
                    print("\n" + "-" * 60)
                    return full_response
                else:
                    return f"Error: Failed to generate response (Status: {response.status_code})"
            else:
                # Non-streaming response
                response = requests.post(
                    f"{self.base_url}/api/generate",
                    json={
                        "model": self.model,
                        "prompt": full_prompt,
                        "stream": False
                    }
                    # No timeout - wait indefinitely for AI response
                )

                if response.status_code == 200:
                    return response.json().get('response', 'No response generated')
                else:
                    return f"Error: Failed to generate response (Status: {response.status_code})"

        except Exception as e:
            return f"Error generating response: {e}"

class NLPQASystem:
    """Main system that combines parsing, indexing, search, and LLM response generation."""

    def __init__(self, nlp_file_paths: List[str] = None, exam_directories: List[str] = None, ollama_model: str = "qwen2.5:4b"):
        # Default file paths and exam directories
        if nlp_file_paths is None:
            nlp_file_paths = ["NLP_Coursera.py", "Tonghop.py"]

        if exam_directories is None:
            exam_directories = [
                # Current exam directories
                "PE_SU24_1", "PE_SU24_2", "KhoaNQA_SE182284_NLP301c",

                # NLP301c_PE_Thanh_gui directories
                "NLP301c_PE_Thanh_gui/NLP301c_FALL23_PE1",
                "NLP301c_PE_Thanh_gui/NLP301c_SP24_PE1",
                "NLP301c_PE_Thanh_gui/NLP301c_SP24_RETAKE",
                "NLP301c_PE_Thanh_gui/NLP301c_SU23_PE1",
                "NLP301c_PE_Thanh_gui/NLP301c_SU23_PE2(retake)",

                # NLP301c-main PE directories
                "NLP301c-main/NLP301c-main/PE/PaperNo_01",
                "NLP301c-main/NLP301c-main/PE/PaperNo_02",
                "NLP301c-main/NLP301c-main/PE/PaperNo_03",
                "NLP301c-main/NLP301c-main/PE/PaperNo_05",
                "NLP301c-main/NLP301c-main/PE/PaperNo_06",
                "NLP301c-main/NLP301c-main/PE/PaperNo_07",
                "NLP301c-main/NLP301c-main/PE/PaperNo_08",
                "NLP301c-main/NLP301c-main/PE/PaperNo_09",
                "NLP301c-main/NLP301c-main/PE/PaperNo_10",
            ]

        self.parser = NLPFileParser(nlp_file_paths, exam_directories)
        self.db_manager = ChromaDBManager()
        self.ollama_client = OllamaClient(model=ollama_model)
        self.qa_pairs = []
    
    def initialize_system(self):
        """Initialize the complete system."""
        logger.info("Initializing NLP QA System...")
        
        # Parse QA pairs from file
        self.qa_pairs = self.parser.parse_file()
        if not self.qa_pairs:
            logger.error("No QA pairs found. Please check the input file.")
            return False
        
        # Initialize ChromaDB
        self.db_manager.initialize_collection()
        
        # Index QA pairs
        self.db_manager.index_qa_pairs(self.qa_pairs)
        
        # Check Ollama availability
        if not self.ollama_client.is_available():
            logger.warning("Ollama service not available. LLM responses will be limited.")
        
        logger.info("System initialization complete!")
        return True
    
    def ask_question(self, question: str, n_results: int = 3, debug: bool = False, stream: bool = False) -> Dict:
        """Process a user question and return comprehensive response."""
        logger.info(f"Processing question: {question}")

        # Search for relevant QA pairs
        search_results = self.db_manager.search_qa_pairs(question, n_results)

        if not search_results:
            return {
                "question": question,
                "search_results": [],
                "llm_response": "No relevant QA pairs found for your question.",
                "status": "no_results"
            }

        # Debug: Show search results details
        if debug:
            print("\n" + "="*80)
            print("🔍 SEARCH RESULTS DETAILS:")
            print("="*80)
            for i, result in enumerate(search_results, 1):
                metadata = result['metadata']
                relevance = 1 - result['distance']
                print(f"\n--- Search Result {i} ---")
                print(f"Question: Q{metadata['question_number']}: {metadata['question_title']}")
                print(f"Type: {metadata.get('question_type', 'coursework')}")
                print(f"Source: {metadata.get('source_file', 'NLP_Coursera.py')}")
                print(f"Relevance: {relevance:.3f}")
                print(f"Content preview: {result['content'][:200]}...")
            print("="*80)

        # Generate LLM response with context
        llm_response = self.ollama_client.generate_response(question, search_results, debug=debug, stream=stream)

        return {
            "question": question,
            "search_results": search_results,
            "llm_response": llm_response,
            "status": "success"
        }
    
    def get_qa_pair_by_number(self, number: int) -> Optional[QAPair]:
        """Get a specific QA pair by its number."""
        for qa_pair in self.qa_pairs:
            if qa_pair.question_number == number:
                return qa_pair
        return None

def main():
    """Main function to demonstrate the system."""
    # Initialize system with coursework and exam questions
    system = NLPQASystem()

    if not system.initialize_system():
        print("Failed to initialize system. Exiting.")
        return
    
    print("\n" + "="*60)
    print("NLP Question-Answer System with Ollama and ChromaDB")
    print("="*60)
    print("Ask questions about NLP concepts and get answers from the coursework!")
    print("Type 'quit' to exit, 'list' to see all available questions")
    print("="*60)
    
    while True:
        try:
            user_input = input("\nYour question: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if user_input.lower() == 'list':
                print("\nAvailable Questions:")
                for qa_pair in system.qa_pairs[:10]:  # Show first 10
                    print(f"{qa_pair.question_number}. {qa_pair.question_title}")
                if len(system.qa_pairs) > 10:
                    print(f"... and {len(system.qa_pairs) - 10} more questions")
                continue
            
            if not user_input:
                continue
            
            # Process the question
            result = system.ask_question(user_input)
            
            print(f"\n{'='*50}")
            print("SEARCH RESULTS:")
            print(f"{'='*50}")
            
            for i, search_result in enumerate(result['search_results'], 1):
                metadata = search_result['metadata']
                relevance = 1 - search_result['distance']
                print(f"\n[{i}] Question {metadata['question_number']}: {metadata['question_title']}")
                print(f"Relevance: {relevance:.3f}")
                print("-" * 40)
                # Show first few lines of content
                content_lines = search_result['content'].split('\n')[:5]
                for line in content_lines:
                    print(line)
                if len(search_result['content'].split('\n')) > 5:
                    print("...")
            
            print(f"\n{'='*50}")
            print("LLM RESPONSE:")
            print(f"{'='*50}")
            print(result['llm_response'])
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
