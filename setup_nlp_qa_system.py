#!/usr/bin/env python3
"""
Setup script for the NLP Question-Answer System
This script helps install dependencies and configure the system.
"""

import subprocess
import sys
import os
import requests
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{description}...")
    try:
        # Use list format to avoid shell issues with spaces in paths
        if isinstance(command, str):
            # For pip commands, use list format
            if "pip install" in command:
                parts = command.split()
                result = subprocess.run(parts, check=True, capture_output=True, text=True)
            else:
                result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        else:
            result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    print("Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"✗ Python 3.8+ required, found {version.major}.{version.minor}")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_requirements():
    """Install Python requirements."""
    if not Path("requirements.txt").exists():
        print("✗ requirements.txt not found")
        return False

    return run_command(
        [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
        "Installing Python requirements"
    )

def check_ollama_installation():
    """Check if Ollama is installed and running."""
    print("\nChecking Ollama installation...")
    
    # Check if Ollama is running
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✓ Ollama is running")
            models = response.json().get('models', [])
            if models:
                print(f"✓ Available models: {[m['name'] for m in models]}")
            else:
                print("⚠ No models found. You may need to pull a model.")
            return True
        else:
            print("✗ Ollama is not responding correctly")
            return False
    except requests.exceptions.RequestException:
        print("✗ Ollama is not running or not installed")
        print("\nTo install Ollama:")
        print("1. Visit: https://ollama.ai/")
        print("2. Download and install Ollama for your OS")
        print("3. Run: ollama pull llama2  (or another model)")
        print("4. Start Ollama service")
        return False

def download_spacy_models():
    """Download required spaCy models."""
    models = ["en_core_web_sm", "en_core_web_lg"]
    success = True

    for model in models:
        if not run_command(
            [sys.executable, "-m", "spacy", "download", model],
            f"Downloading spaCy model: {model}"
        ):
            success = False

    return success

def create_directories():
    """Create necessary directories."""
    directories = ["chroma_db", "logs"]
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"✓ Created directory: {directory}")
        except Exception as e:
            print(f"✗ Failed to create directory {directory}: {e}")
            return False
    
    return True

def test_system():
    """Test if the system can be imported and initialized."""
    print("\nTesting system import...")
    try:
        # Test import
        import nlp_qa_system
        print("✓ System import successful")
        
        # Test basic initialization (without full setup)
        parser = nlp_qa_system.NLPFileParser("NLP_Coursera.py")
        if Path("NLP_Coursera.py").exists():
            print("✓ NLP_Coursera.py file found")
        else:
            print("⚠ NLP_Coursera.py file not found in current directory")
        
        return True
    except ImportError as e:
        print(f"✗ System import failed: {e}")
        return False

def main():
    """Main setup function."""
    print("="*60)
    print("NLP Question-Answer System Setup")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        print("⚠ Some directories could not be created")
    
    # Install requirements
    if not install_requirements():
        print("✗ Failed to install requirements")
        sys.exit(1)
    
    # Download spaCy models
    if not download_spacy_models():
        print("⚠ Some spaCy models could not be downloaded")
    
    # Check Ollama
    ollama_ok = check_ollama_installation()
    
    # Test system
    if not test_system():
        print("✗ System test failed")
        sys.exit(1)
    
    print("\n" + "="*60)
    print("SETUP SUMMARY")
    print("="*60)
    print("✓ Python requirements installed")
    print("✓ System can be imported")
    
    if ollama_ok:
        print("✓ Ollama is ready")
    else:
        print("⚠ Ollama needs to be installed/configured")
    
    print("\n" + "="*60)
    print("NEXT STEPS")
    print("="*60)
    print("1. Ensure NLP_Coursera.py is in the current directory")
    
    if not ollama_ok:
        print("2. Install and configure Ollama:")
        print("   - Visit https://ollama.ai/")
        print("   - Install Ollama")
        print("   - Run: ollama pull llama2")
        print("   - Start Ollama service")
    
    print("3. Run the system:")
    print("   python nlp_qa_system.py")
    
    print("\n✓ Setup complete!")

if __name__ == "__main__":
    main()
