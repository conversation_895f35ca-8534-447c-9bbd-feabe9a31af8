""""Question 3: (3 marks)
Write a function that takes a text and a vocabulary as its arguments and returns the set of
words that appear in the text but not in the vocabulary. Both arguments can be represented as lists
of strings.
Example:
Input:
text = 'a text and a vocabulary
vocab = 'a vocabulary'
Output:
['and', 'text'] """

import nltk
from nltk.tokenize import word_tokenize

# Ensure you have the required NLTK data
nltk.download('punkt')


def words_not_in_vocab(text, vocab):
    # Tokenize the text and vocabulary using NLTK
    text_words = set(word_tokenize(text.lower()))
    vocab_words = set(word_tokenize(vocab.lower()))

    # Find words in text that are not in the vocabulary
    missing_words = text_words - vocab_words

    return list(missing_words)


# Example usage
text = 'a text and a vocabulary'
vocab = 'a vocabulary'
print(words_not_in_vocab(text, vocab))
