#!/usr/bin/env python3
"""
Tool to view all questions stored in the NLP QA database
"""

from nlp_qa_system import NLPQASystem
import j<PERSON>

def view_all_questions():
    """Display all questions stored in the database."""
    print("🔍 Loading NLP QA System...")
    
    # Initialize system
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    print(f"✅ Loaded {len(system.qa_pairs)} questions from database")
    
    # Group questions by type
    coursework_questions = []
    previous_exam_questions = []
    exam_questions = []
    
    for qa_pair in system.qa_pairs:
        if qa_pair.question_type == "coursework":
            coursework_questions.append(qa_pair)
        elif qa_pair.question_type == "previous_exam":
            previous_exam_questions.append(qa_pair)
        elif qa_pair.question_type == "exam":
            exam_questions.append(qa_pair)
    
    # Sort by question number
    coursework_questions.sort(key=lambda x: x.question_number)
    previous_exam_questions.sort(key=lambda x: x.question_number)
    exam_questions.sort(key=lambda x: x.question_number)
    
    print(f"\n{'='*80}")
    print("📊 DATABASE SUMMARY")
    print(f"{'='*80}")
    print(f"📚 Coursework questions: {len(coursework_questions)}")
    print(f"📝 Previous exam questions: {len(previous_exam_questions)}")
    print(f"🎯 External exam questions: {len(exam_questions)}")
    print(f"📋 Total questions: {len(system.qa_pairs)}")
    
    # Display each category
    if coursework_questions:
        display_question_category("📚 COURSEWORK QUESTIONS", coursework_questions)
    
    if previous_exam_questions:
        display_question_category("📝 PREVIOUS EXAM QUESTIONS", previous_exam_questions)
    
    if exam_questions:
        display_question_category("🎯 EXTERNAL EXAM QUESTIONS", exam_questions)

def display_question_category(title, questions):
    """Display questions in a specific category."""
    print(f"\n{'='*80}")
    print(title)
    print(f"{'='*80}")
    
    for i, qa_pair in enumerate(questions, 1):
        print(f"\n[{i}] Q{qa_pair.question_number}: {qa_pair.question_title}")
        
        if qa_pair.question_description and qa_pair.question_description != qa_pair.question_title:
            print(f"    📖 Description: {qa_pair.question_description}")
        
        print(f"    📁 Source: {qa_pair.source_file}")
        print(f"    🏷️  Type: {qa_pair.question_type}")
        
        # Show code preview (first 3 lines)
        code_lines = qa_pair.answer_code.strip().split('\n')
        if code_lines and code_lines[0]:
            print(f"    💻 Code preview:")
            for j, line in enumerate(code_lines[:3]):
                if line.strip():
                    print(f"       {line}")
            if len(code_lines) > 3:
                print(f"       ... ({len(code_lines) - 3} more lines)")
        
        print(f"    📏 Code length: {len(qa_pair.answer_code)} characters")

def view_specific_question(question_number):
    """View a specific question in detail."""
    print(f"🔍 Loading question {question_number}...")
    
    # Initialize system
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    # Find the question
    qa_pair = system.get_qa_pair_by_number(question_number)
    
    if not qa_pair:
        print(f"❌ Question {question_number} not found")
        print(f"Available question numbers: {sorted([q.question_number for q in system.qa_pairs])}")
        return
    
    print(f"\n{'='*80}")
    print(f"📝 QUESTION {qa_pair.question_number} DETAILS")
    print(f"{'='*80}")
    print(f"Title: {qa_pair.question_title}")
    
    if qa_pair.question_description:
        print(f"Description: {qa_pair.question_description}")
    
    print(f"Type: {qa_pair.question_type}")
    print(f"Source: {qa_pair.source_file}")
    
    print(f"\n💻 COMPLETE CODE SOLUTION:")
    print("-" * 60)
    print(qa_pair.answer_code)
    print("-" * 60)
    
    print(f"\n📄 FULL CONTENT (as stored in database):")
    print("-" * 60)
    print(qa_pair.full_content)
    print("-" * 60)

def search_questions(search_term):
    """Search for questions containing a specific term."""
    print(f"🔍 Searching for questions containing: '{search_term}'")
    
    # Initialize system
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    # Search in titles and descriptions
    matching_questions = []
    
    for qa_pair in system.qa_pairs:
        if (search_term.lower() in qa_pair.question_title.lower() or 
            search_term.lower() in qa_pair.question_description.lower() or
            search_term.lower() in qa_pair.answer_code.lower()):
            matching_questions.append(qa_pair)
    
    if not matching_questions:
        print(f"❌ No questions found containing '{search_term}'")
        return
    
    print(f"✅ Found {len(matching_questions)} questions containing '{search_term}':")
    
    for qa_pair in matching_questions:
        print(f"\nQ{qa_pair.question_number}: {qa_pair.question_title} ({qa_pair.question_type})")
        if qa_pair.question_description:
            print(f"   📖 {qa_pair.question_description}")

def export_questions_to_json():
    """Export all questions to a JSON file for inspection."""
    print("📤 Exporting questions to JSON...")
    
    # Initialize system
    system = NLPQASystem()
    
    if not system.initialize_system():
        print("❌ Failed to initialize system")
        return
    
    # Convert to JSON-serializable format
    questions_data = []
    
    for qa_pair in system.qa_pairs:
        questions_data.append({
            "question_number": qa_pair.question_number,
            "question_title": qa_pair.question_title,
            "question_description": qa_pair.question_description,
            "question_type": qa_pair.question_type,
            "source_file": qa_pair.source_file,
            "answer_code": qa_pair.answer_code,
            "full_content": qa_pair.full_content,
            "code_length": len(qa_pair.answer_code)
        })
    
    # Sort by question number
    questions_data.sort(key=lambda x: x["question_number"])
    
    # Export to JSON
    with open("database_questions.json", "w", encoding="utf-8") as f:
        json.dump(questions_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Exported {len(questions_data)} questions to 'database_questions.json'")
    print("You can open this file in any text editor to inspect the questions")

def main():
    """Main function with menu options."""
    print("="*80)
    print("🔍 NLP QA Database Question Viewer")
    print("="*80)
    
    while True:
        print("\nChoose an option:")
        print("1. 📋 View all questions (summary)")
        print("2. 🔍 View specific question (detailed)")
        print("3. 🔎 Search questions")
        print("4. 📤 Export to JSON file")
        print("5. 🚪 Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            view_all_questions()
        elif choice == '2':
            try:
                question_num = int(input("Enter question number: "))
                view_specific_question(question_num)
            except ValueError:
                print("❌ Please enter a valid number")
        elif choice == '3':
            search_term = input("Enter search term: ").strip()
            if search_term:
                search_questions(search_term)
            else:
                print("❌ Please enter a search term")
        elif choice == '4':
            export_questions_to_json()
        elif choice == '5':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-5.")

if __name__ == "__main__":
    main()
