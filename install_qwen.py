#!/usr/bin/env python3
"""
Script to install Qwen2.5:4b model for Ollama
"""

import subprocess
import sys
import requests
import time

def check_ollama_running():
    """Check if Ollama service is running."""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        return response.status_code == 200
    except:
        return False

def install_qwen_model():
    """Install Qwen2.5:4b model."""
    print("🚀 Installing Qwen2.5:4b model...")
    
    if not check_ollama_running():
        print("❌ Ollama service is not running.")
        print("Please start Ollama first:")
        print("1. Install Ollama from https://ollama.ai/")
        print("2. Start the service")
        return False
    
    try:
        # Pull the Qwen2.5:4b model
        print("📥 Pulling Qwen2.5:4b model (this may take a few minutes)...")
        result = subprocess.run(
            ["ollama", "pull", "qwen2.5:4b"],
            check=True,
            capture_output=True,
            text=True
        )
        
        print("✅ Qwen2.5:4b model installed successfully!")
        
        # Test the model
        print("🧪 Testing the model...")
        test_result = subprocess.run(
            ["ollama", "run", "qwen2.5:4b", "Hello, can you help with NLP questions?"],
            check=True,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print("✅ Model test successful!")
        print(f"Sample response: {test_result.stdout[:100]}...")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install model: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
        return False
    except subprocess.TimeoutExpired:
        print("⚠️ Model test timed out, but installation likely succeeded")
        return True
    except FileNotFoundError:
        print("❌ Ollama command not found.")
        print("Please install Ollama from https://ollama.ai/")
        return False

def list_available_models():
    """List all available models."""
    try:
        result = subprocess.run(
            ["ollama", "list"],
            check=True,
            capture_output=True,
            text=True
        )
        
        print("📋 Available models:")
        print(result.stdout)
        
    except Exception as e:
        print(f"❌ Failed to list models: {e}")

def main():
    """Main function."""
    print("="*60)
    print("Qwen2.5:4b Model Installation for NLP QA System")
    print("="*60)
    
    if install_qwen_model():
        print("\n" + "="*60)
        print("✅ INSTALLATION COMPLETE!")
        print("="*60)
        print("You can now use the NLP QA System with Qwen2.5:4b model:")
        print("python run_nlp_qa.py --model qwen2.5:4b")
        print("\nOr simply run (Qwen2.5:4b is now the default):")
        print("python run_nlp_qa.py")
        
        print("\n📋 All installed models:")
        list_available_models()
        
    else:
        print("\n❌ Installation failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
